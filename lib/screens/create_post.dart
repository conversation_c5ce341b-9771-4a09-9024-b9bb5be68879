import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import 'dart:developer' as developer;
import '../services/wallet_service.dart';
import '../services/post_service.dart';
import '../services/category_preference_service.dart';
import '../screens/media_editor_screen.dart';
import 'wallet_screen.dart';

class CreatePostScreen extends StatefulWidget {
  const CreatePostScreen({super.key});

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final _contentController = TextEditingController();
  final WalletService _walletService = WalletService();
  final PostService _postService = PostService();

  double _postPrice = 0.05; // Default minimum, will be updated from Firestore
  bool _isLoading = false;
  bool _isSubmitting = false;
  String? _selectedCategory;

  // Media upload variables
  final List<XFile> _selectedImages = [];
  final List<XFile> _selectedVideos = [];
  final List<XFile> _selectedVideoAds = [];
  String? _linkUrl;
  final ImagePicker _imagePicker = ImagePicker();

  // Character limit
  static const int _maxCharacters = 480;

  // Available categories with their themes
  final List<Map<String, dynamic>> _categories = [
    {
      'name': 'Politics',
      'icon': Icons.how_to_vote,
      'color': const Color(0xFF4C5DFF),
    },
    {'name': 'News', 'icon': Icons.newspaper, 'color': const Color(0xFF29CC76)},
    {
      'name': 'Sports',
      'icon': Icons.sports_soccer,
      'color': const Color(0xFFC43DFF),
    },
    {'name': 'Sex', 'icon': Icons.favorite, 'color': const Color(0xFFFF4081)},
    {
      'name': 'Entertainment',
      'icon': Icons.movie,
      'color': const Color(0xFFA06A00),
    },
    {
      'name': 'Religion',
      'icon': Icons.church,
      'color': const Color(0xFF000000),
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeCategory();
  }

  Future<void> _initializeServices() async {
    try {
      await _walletService.initialize();
      await _postService.initialize();
      await _loadSavedPostAmount();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services in CreatePostScreen: $e');
    }
  }

  Future<void> _loadSavedPostAmount() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    try {
      final doc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .get();

      if (doc.exists && doc.data()!.containsKey('postAmount')) {
        final savedAmount = (doc.data()!['postAmount'] as num).toDouble();
        if (mounted) {
          setState(() {
            _postPrice = savedAmount;
          });
          debugPrint(
            'Loaded saved post amount: \$${savedAmount.toStringAsFixed(2)}',
          );
        }
      }
    } catch (e) {
      debugPrint('Failed to load saved post amount: $e');
      // Keep default value if loading fails
    }
  }

  Future<void> _initializeCategory() async {
    // Load user's selected category from Firestore
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userDoc =
            await FirebaseFirestore.instance
                .collection('users')
                .doc(user.uid)
                .get();

        if (userDoc.exists) {
          final userData = userDoc.data();
          final userCategory = userData?['selectedCategory'];

          if (mounted) {
            setState(() {
              _selectedCategory =
                  userCategory ?? 'News'; // Default to News if not set
            });
          }
        }
      }
    } catch (e) {
      // Fallback to default category
      if (mounted) {
        setState(() {
          _selectedCategory = 'News';
        });
      }
    }
  }

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(image);
        });
      }
    } catch (e) {
      _showErrorMessage('Failed to pick image: $e');
    }
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(image);
        });
      }
    } catch (e) {
      _showErrorMessage('Failed to take photo: $e');
    }
  }

  Future<void> _pickVideo() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(
          seconds: 30,
        ), // 30 second limit for regular videos
      );

      if (video != null) {
        setState(() {
          _selectedVideos.add(video);
        });
      }
    } catch (e) {
      _showErrorMessage('Failed to pick video: $e');
    }
  }

  Future<void> _recordVideo() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(
          seconds: 30,
        ), // 30 second limit for regular videos
      );

      if (video != null) {
        setState(() {
          _selectedVideos.add(video);
        });
      }
    } catch (e) {
      _showErrorMessage('Failed to record video: $e');
    }
  }

  Future<void> _pickVideoAd() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(
          seconds: 60,
        ), // 60 second limit for video ads
      );

      if (video != null) {
        setState(() {
          _selectedVideoAds.add(video);
        });
      }
    } catch (e) {
      _showErrorMessage('Failed to pick video ad: $e');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  void _removeVideoAd(int index) {
    setState(() {
      _selectedVideoAds.removeAt(index);
    });
  }

  void _editMedia(XFile mediaFile, bool isVideo, int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => MediaEditorScreen(
              mediaFile: mediaFile,
              isVideo: isVideo,
              onSave: (editedFile) {
                setState(() {
                  if (isVideo) {
                    _selectedVideos[index] = editedFile;
                  } else {
                    _selectedImages[index] = editedFile;
                  }
                });
              },
            ),
      ),
    );
  }

  void _showAddLinkDialog() {
    final TextEditingController linkController = TextEditingController(
      text: _linkUrl,
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add Link'),
            content: TextField(
              controller: linkController,
              decoration: const InputDecoration(
                hintText: 'Enter URL (https://...)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.url,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  final url = linkController.text.trim();
                  if (url.isNotEmpty) {
                    setState(() {
                      _linkUrl = url;
                    });
                  }
                  Navigator.pop(context);
                },
                child: const Text('Add'),
              ),
            ],
          ),
    );
  }

  void _removeLink() {
    setState(() {
      _linkUrl = null;
    });
  }

  void _insertEmoji(String emoji) {
    final text = _contentController.text;
    final selection = _contentController.selection;
    final newText = text.replaceRange(selection.start, selection.end, emoji);

    if (newText.length <= _maxCharacters) {
      _contentController.text = newText;
      _contentController.selection = TextSelection.collapsed(
        offset: selection.start + emoji.length,
      );
    }
  }

  Future<List<String>> _uploadPostImages() async {
    if (_selectedImages.isEmpty) {
      return [];
    }

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    List<String> imageUrls = [];

    for (int i = 0; i < _selectedImages.length; i++) {
      final image = _selectedImages[i];
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final ref = FirebaseStorage.instance
          .ref()
          .child('post_images')
          .child(user.uid)
          .child('${timestamp}_$i.jpg');

      debugPrint(
        '🔄 Starting post image upload ${i + 1}/${_selectedImages.length}...',
      );
      debugPrint('📁 Upload path: ${ref.fullPath}');

      // Retry mechanism for image upload
      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          UploadTask uploadTask;

          if (kIsWeb) {
            final bytes = await image.readAsBytes();
            uploadTask = ref.putData(bytes);
          } else {
            uploadTask = ref.putFile(File(image.path));
          }

          // Upload with timeout
          final snapshot = await uploadTask.timeout(
            const Duration(seconds: 120),
          );

          // Verify upload was successful
          if (snapshot.state == TaskState.success) {
            final downloadUrl = await snapshot.ref.getDownloadURL().timeout(
              const Duration(seconds: 30),
            );
            debugPrint('✅ Post image uploaded successfully: $downloadUrl');
            developer.log(
              'Post image uploaded successfully: $downloadUrl',
              name: 'PostImageUpload',
            );
            imageUrls.add(downloadUrl);
            break; // Break out of retry loop for this image
          } else {
            throw Exception('Upload failed with state: ${snapshot.state}');
          }
        } on FirebaseException catch (e) {
          debugPrint(
            '❌ Firebase error on attempt $attempt: ${e.code} - ${e.message}',
          );
          debugPrint('🔍 Full error details: $e');
          debugPrint('📁 Attempted path: ${ref.fullPath}');
          developer.log(
            'Firebase error on attempt $attempt: ${e.code} - ${e.message}',
            name: 'PostImageUpload',
            error: e,
          );

          // Handle specific Firebase errors
          if (e.code == 'object-not-found') {
            debugPrint('🚫 Object not found error during post image upload');
          } else if (e.code == 'unauthorized') {
            debugPrint(
              '🔐 Unauthorized error - check Firebase Storage rules for post_images path',
            );
            throw Exception(
              'Unauthorized to upload image. Please check permissions.',
            );
          } else if (e.code == 'canceled') {
            debugPrint('⏹️ Post image upload was canceled');
            throw Exception('Upload was canceled.');
          }

          if (attempt == 3) {
            throw Exception(
              'Failed to upload post image after 3 attempts: ${e.message}',
            );
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        } catch (e) {
          debugPrint('General error on attempt $attempt: $e');
          if (attempt == 3) {
            throw Exception('Failed to upload post image after 3 attempts: $e');
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        }
      }
    }

    return imageUrls;
  }

  Future<List<String>> _uploadPostVideos() async {
    if (_selectedVideos.isEmpty) {
      return [];
    }

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    List<String> videoUrls = [];

    for (int i = 0; i < _selectedVideos.length; i++) {
      final video = _selectedVideos[i];
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final ref = FirebaseStorage.instance
          .ref()
          .child('post_videos')
          .child(user.uid)
          .child('${timestamp}_$i.mp4');

      debugPrint(
        '🔄 Starting post video upload ${i + 1}/${_selectedVideos.length}...',
      );
      debugPrint('📁 Upload path: ${ref.fullPath}');

      // Retry mechanism for video upload
      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          UploadTask uploadTask;

          if (kIsWeb) {
            final bytes = await video.readAsBytes();
            uploadTask = ref.putData(bytes);
          } else {
            uploadTask = ref.putFile(File(video.path));
          }

          // Upload with timeout (longer for videos)
          final snapshot = await uploadTask.timeout(const Duration(minutes: 5));

          // Verify upload was successful
          if (snapshot.state == TaskState.success) {
            final downloadUrl = await snapshot.ref.getDownloadURL().timeout(
              const Duration(seconds: 30),
            );
            debugPrint('✅ Post video uploaded successfully: $downloadUrl');
            developer.log(
              'Post video uploaded successfully: $downloadUrl',
              name: 'PostVideoUpload',
            );
            videoUrls.add(downloadUrl);
            break; // Break out of retry loop for this video
          } else {
            throw Exception('Upload failed with state: ${snapshot.state}');
          }
        } on FirebaseException catch (e) {
          debugPrint(
            '❌ Firebase error on attempt $attempt: ${e.code} - ${e.message}',
          );

          if (attempt == 3) {
            throw Exception(
              'Failed to upload post video after 3 attempts: ${e.message}',
            );
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        } catch (e) {
          debugPrint('General error on attempt $attempt: $e');
          if (attempt == 3) {
            throw Exception('Failed to upload post video after 3 attempts: $e');
          }
          // Wait before retrying
          await Future.delayed(Duration(seconds: attempt * 2));
        }
      }
    }

    return videoUrls;
  }

  Map<String, dynamic> _getCategoryData(String categoryName) {
    return _categories.firstWhere(
      (cat) => cat['name'] == categoryName,
      orElse: () => _categories[0],
    );
  }

  String _getCategoryPrompt(String categoryName) {
    switch (categoryName) {
      case 'Politics':
        return "Share your political views and engage in meaningful political discourse";
      case 'News':
        return "Don't miss out on trending news happening around the world, share breaking stories and current events";
      case 'Sports':
        return "Discuss games, athletes, teams, and sporting events happening worldwide";
      case 'Sex':
        return "Share mature content and adult discussions in a respectful manner";
      case 'Entertainment':
        return "Discuss movies, TV shows, music, celebrities, and entertainment industry news";
      case 'Religion':
        return "Share your faith, spiritual insights, and engage in respectful religious discussions";
      default:
        return "Share your thoughts and engage with the community";
    }
  }

  Future<void> _publishPost() async {
    if (_isSubmitting) return;

    if (_contentController.text.trim().isEmpty) {
      _showErrorMessage('Please write something to share');
      return;
    }

    if (_contentController.text.length > _maxCharacters) {
      _showErrorMessage('Post exceeds $_maxCharacters character limit');
      return;
    }

    if (_selectedCategory == null) {
      _showErrorMessage('Please select a category');
      return;
    }

    // Check wallet balance
    final currentBalance = _walletService.currentBalance;
    if (_postPrice > currentBalance) {
      _showErrorMessage(
        'Insufficient funds. Your balance: ${_walletService.formatCurrency(currentBalance)}',
        showAddFunds: true,
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
      _isLoading = true;
    });

    try {
      // Upload images if selected
      List<String> imageUrls = [];
      if (_selectedImages.isNotEmpty) {
        try {
          imageUrls = await _uploadPostImages();
          debugPrint('Images uploaded successfully for post: $imageUrls');
        } catch (e) {
          debugPrint('Image upload failed: $e');
          _showErrorMessage('Failed to upload images: $e');
          return; // Don't proceed if image upload fails
        }
      }

      // Upload videos if selected
      List<String> videoUrls = [];
      if (_selectedVideos.isNotEmpty) {
        try {
          videoUrls = await _uploadPostVideos();
          debugPrint('Videos uploaded successfully for post: $videoUrls');
        } catch (e) {
          debugPrint('Video upload failed: $e');
          _showErrorMessage('Failed to upload videos: $e');
          return; // Don't proceed if video upload fails
        }
      }

      // Deduct balance for post creation
      final balanceDeducted = await _walletService.deductBalance(
        _postPrice,
        'Post creation in $_selectedCategory',
        postId: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (!balanceDeducted) {
        throw Exception('Failed to process payment. Please try again.');
      }

      // Create the post with images, videos and link
      final postId = await _postService.createPost(
        content: _contentController.text.trim(),
        price: _postPrice,
        category: _selectedCategory!,
        tags: [],
        isPublic: true,
        allowComments: true,
        imageUrls: imageUrls,
        videoUrls: videoUrls,
        linkUrl: _linkUrl,
      );

      if (mounted) {
        HapticFeedback.mediumImpact();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Post published in $_selectedCategory! Amount: ${_walletService.formatCurrency(_postPrice)}',
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );

        // Check if we're in a navigation stack or IndexedStack
        if (Navigator.canPop(context)) {
          Navigator.pop(context, {
            'success': true,
            'postId': postId,
            'category': _selectedCategory,
            'price': _postPrice,
          });
        } else {
          // We're in an IndexedStack, use a different approach
          // Clear the form and show success message
          _contentController.clear();
          setState(() {
            _selectedImages.clear();
            _selectedVideos.clear();
            _linkUrl = null;
            _postPrice = 0.05;
            _selectedCategory = null;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage(
          'Failed to create post: ${e.toString().replaceAll('Exception: ', '')}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
          _isLoading = false;
        });
      }
    }
  }

  void _showCategoryPicker() {
    // Category selection is now fixed based on user's profile
    // This method is kept for compatibility but does nothing
    return;
  }

  void _showErrorMessage(String message, {bool showAddFunds = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        action:
            showAddFunds
                ? SnackBarAction(
                  label: 'ReUp!',
                  textColor: Colors.white,
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WalletScreen(),
                      ),
                    ).then((_) => setState(() {}));
                  },
                )
                : null,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final categoryData = _getCategoryData(_selectedCategory ?? 'Politics');
    final categoryColor = categoryData['color'] as Color;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Center(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: kIsWeb ? 800 : double.infinity,
                ),
                margin:
                    kIsWeb
                        ? const EdgeInsets.symmetric(horizontal: 24)
                        : EdgeInsets.zero,
                child: Column(
                  children: [
                    // Header Section
                    Container(
                      color: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                      child: Column(
                        children: [
                          // Top Header with Logo (centered)
                          Center(
                            child: Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(25),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(25),
                                child: Image.asset(
                                  'assets/images/money_mouth.png',
                                  width: 50,
                                  height: 50,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          //    // Tab Bar Row (Explore and Following)
                          //     Row(
                          //       children: [
                          //         // Explore Tab
                          //         Expanded(
                          //           child: GestureDetector(
                          //             onTap: () {
                          //               setState(() {
                          //                 _selectedTab = 'Explore';
                          //               });
                          //             },
                          //             child: Column(
                          //               children: [
                          //                 Text(
                          //                   'Explore',
                          //                   style: TextStyle(
                          //                     fontSize: 16,
                          //                     color:
                          //                         _selectedTab == 'Explore'
                          //                             ? Colors.black
                          //                             : Colors.grey[600],
                          //                     fontWeight:
                          //                         _selectedTab == 'Explore'
                          //                             ? FontWeight.w600
                          //                             : FontWeight.w500,
                          //                   ),
                          //                 ),
                          //                 const SizedBox(height: 4),
                          //                 Container(
                          //                   height: 2,
                          //                   color:
                          //                       _selectedTab == 'Explore'
                          //                           ? const Color(0xFF5159FF)
                          //                           : Colors.transparent,
                          //                 ),
                          //               ],
                          //             ),
                          //           ),
                          //         ),
                          //         // Following Tab
                          //         Expanded(
                          //           child: GestureDetector(
                          //             onTap: () {
                          //               setState(() {
                          //                 _selectedTab = 'Following';
                          //               });
                          //             },
                          //             child: Column(
                          //               children: [
                          //                 Text(
                          //                   'Following',
                          //                   textAlign: TextAlign.center,
                          //                   style: TextStyle(
                          //                     fontSize: 16,
                          //                     color:
                          //                         _selectedTab == 'Following'
                          //                             ? Colors.black
                          //                             : Colors.grey[600],
                          //                     fontWeight:
                          //                         _selectedTab == 'Following'
                          //                             ? FontWeight.w600
                          //                             : FontWeight.w500,
                          //                   ),
                          //                 ),
                          //                 const SizedBox(height: 4),
                          //                 Container(
                          //                   height: 2,
                          //                   color:
                          //                       _selectedTab == 'Following'
                          //                           ? const Color(0xFF5159FF)
                          //                           : Colors.transparent,
                          //                 ),
                          //               ],
                          //             ),
                          //           ),
                          //         ),
                          //       ],
                          //     ),

                          //     const SizedBox(height: 20),

                          // Category Section
                          // Container(
                          //   padding: const EdgeInsets.all(16),
                          //   decoration: BoxDecoration(
                          //     color: Colors.grey[100],
                          //     borderRadius: BorderRadius.circular(12),
                          //   ),
                          //   child: Row(
                          //     children: [
                          //       GestureDetector(
                          //         onTap: () {
                          //           if (Navigator.canPop(context)) {
                          //             Navigator.pop(context);
                          //           }
                          //           // If we can't pop, we're in IndexedStack - do nothing
                          //         },
                          //         child: Icon(
                          //           Icons.arrow_back,
                          //           color: Colors.grey[600],
                          //           size: 20,
                          //         ),
                          //       ),
                          //       const SizedBox(width: 12),
                          //       Expanded(
                          //         child: Column(
                          //           crossAxisAlignment:
                          //               CrossAxisAlignment.start,
                          //           children: [
                          //             Text(
                          //               '${_selectedCategory ?? 'Politics'} Category',
                          //               style: TextStyle(
                          //                 fontSize: 14,
                          //                 color: Colors.grey[800],
                          //                 fontWeight: FontWeight.w600,
                          //               ),
                          //             ),
                          //             const SizedBox(height: 2),
                          //             Text(
                          //               'Featuring latest news trends and more',
                          //               style: TextStyle(
                          //                 fontSize: 12,
                          //                 color: Colors.grey[500],
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       ),
                          //       const SizedBox(width: 12),
                          //       Container(
                          //         padding: const EdgeInsets.symmetric(
                          //           horizontal: 16,
                          //           vertical: 6,
                          //         ),
                          //         decoration: BoxDecoration(
                          //           color: categoryColor,
                          //           borderRadius: BorderRadius.circular(20),
                          //         ),
                          //         child: Row(
                          //           mainAxisSize: MainAxisSize.min,
                          //           children: [
                          //             Icon(
                          //               Icons.category,
                          //               size: 14,
                          //               color: Colors.white,
                          //             ),
                          //             const SizedBox(width: 4),
                          //             Text(
                          //               _selectedCategory ?? 'News',
                          //               style: const TextStyle(
                          //                 color: Colors.white,
                          //                 fontWeight: FontWeight.w600,
                          //                 fontSize: 14,
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                        ],
                      ),
                    ),

                    // Main Content Area
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(0),
                        child: _buildExpandedView(categoryColor),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildExpandedView(Color categoryColor) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Text(
              '${_selectedCategory ?? 'Politics'} today!',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),

            const SizedBox(height: 12),

            // Description
            // Text(
            //   _getCategoryPrompt(_selectedCategory ?? 'Politics'),
            //   style: TextStyle(
            //     fontSize: 14,
            //     color: Colors.grey[600],
            //     height: 1.4,
            //   ),
            // ),
            const SizedBox(height: 20),

            // Content Input
            Container(
              height: 250, // Fixed height for text input area
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: TextFormField(
                controller: _contentController,
                decoration: InputDecoration(
                  hintText:
                      'Share your thoughts about ${_selectedCategory?.toLowerCase()}...',
                  hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
                style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                maxLines: null,
                textAlignVertical: TextAlignVertical.top,
                onChanged: (value) {
                  setState(() {
                    // This will trigger a rebuild and update character count in real-time
                  });
                },
              ),
            ),

            const SizedBox(height: 16),

            // Media upload section
            _buildMediaSection(),

            const SizedBox(height: 16),

            // Character count
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_contentController.text.length}/$_maxCharacters characters',
                  style: TextStyle(
                    fontSize: 12,
                    color:
                        _contentController.text.length > _maxCharacters
                            ? Colors.red
                            : Colors.grey[500],
                  ),
                ),
                Text(
                  'Cost: ${_walletService.formatCurrency(_postPrice)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: categoryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Say It Button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: (_isLoading || _isSubmitting) ? null : _publishPost,
                style: ElevatedButton.styleFrom(
                  backgroundColor: categoryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child:
                    (_isLoading || _isSubmitting)
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                        : const Text(
                          'Put Up',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Media upload buttons - scrollable to prevent overflow
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildMediaButton(
                icon: Icons.photo_library,
                label: 'Photo',
                onTap: _pickImage,
                gradientStart: const Color(0xFF232526),
                gradientEnd: const Color(0xFF414345),
              ),
              const SizedBox(width: 12),
              _buildMediaButton(
                icon: Icons.camera_alt,
                label: 'Camera',
                onTap: _takePhoto,
                gradientStart: const Color(0xFF232526),
                gradientEnd: const Color(0xFF414345),
              ),
              const SizedBox(width: 12),
              _buildMediaButton(
                icon: Icons.videocam,
                label: 'Video',
                onTap: _pickVideo,
                gradientStart: const Color(0xFF232526),
                gradientEnd: const Color(0xFF414345),
              ),
              const SizedBox(width: 12),
              _buildMediaButton(
                icon: Icons.video_camera_back,
                label: 'Record',
                onTap: _recordVideo,
                gradientStart: const Color(0xFF232526),
                gradientEnd: const Color(0xFF414345),
              ),
              const SizedBox(width: 12),
              _buildMediaButton(
                icon: Icons.ads_click,
                label: 'Video Ad',
                onTap: _pickVideoAd,
                gradientStart: const Color(0xFF232526),
                gradientEnd: const Color(0xFF414345),
              ),
              const SizedBox(width: 12),
              _buildMediaButton(
                icon: Icons.link,
                label: 'Link',
                onTap: _showAddLinkDialog,
                gradientStart: const Color(0xFF232526),
                gradientEnd: const Color(0xFF414345),
              ),
              const SizedBox(width: 12),
              _buildMediaButton(
                icon: Icons.emoji_emotions,
                label: 'Emoji',
                onTap: _showEmojiPicker,
              ),
              const SizedBox(width: 12), // Extra padding at the end
            ],
          ),
        ),

        // Show selected attachments in a separate scrollable section
        if (_selectedImages.isNotEmpty ||
            _selectedVideos.isNotEmpty ||
            _selectedVideoAds.isNotEmpty) ...[
          const SizedBox(height: 12),
          SizedBox(
            height: 120,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Show selected images with preview
                  ..._selectedImages.asMap().entries.map((entry) {
                    final index = entry.key;
                    final image = entry.value;
                    return Container(
                      width: 100,
                      height: 100,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child:
                                kIsWeb
                                    ? FutureBuilder<Uint8List>(
                                      future: image.readAsBytes(),
                                      builder: (context, snapshot) {
                                        if (snapshot.hasData) {
                                          return Image.memory(
                                            snapshot.data!,
                                            width: 100,
                                            height: 100,
                                            fit: BoxFit.cover,
                                          );
                                        }
                                        return Container(
                                          width: 100,
                                          height: 100,
                                          color: Colors.grey[200],
                                          child: const Icon(Icons.image),
                                        );
                                      },
                                    )
                                    : Image.file(
                                      File(image.path),
                                      width: 100,
                                      height: 100,
                                      fit: BoxFit.cover,
                                    ),
                          ),
                          // Edit button
                          Positioned(
                            bottom: 4,
                            left: 4,
                            child: GestureDetector(
                              onTap: () => _editMedia(image, false, index),
                              child: Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: Colors.blue.withValues(alpha: 0.8),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.edit,
                                  size: 14,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                          // Remove button
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () => _removeImage(index),
                              child: Container(
                                width: 20,
                                height: 20,
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  size: 12,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }),

                  // Show selected videos with preview
                  ..._selectedVideos.asMap().entries.map((entry) {
                    final index = entry.key;
                    // final video = entry.value; // Unused variable
                    return Container(
                      width: 100,
                      height: 100,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                        color: Colors.grey[200],
                      ),
                      child: Stack(
                        children: [
                          const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.videocam,
                                  size: 24,
                                  color: Colors.blue,
                                ),
                                SizedBox(height: 4),
                                Text(
                                  'Video',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.blue,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () => _removeVideo(index),
                              child: Container(
                                width: 20,
                                height: 20,
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  size: 12,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }),

                  // Show selected video ads with preview
                  ..._selectedVideoAds.asMap().entries.map((entry) {
                    final index = entry.key;
                    return Container(
                      width: 100,
                      height: 100,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange[300]!),
                        color: Colors.orange[50],
                      ),
                      child: Stack(
                        children: [
                          const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.ads_click,
                                  size: 24,
                                  color: Colors.orange,
                                ),
                                SizedBox(height: 4),
                                Text(
                                  'Video Ad',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.orange,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () => _removeVideoAd(index),
                              child: Container(
                                width: 20,
                                height: 20,
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  size: 12,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        ],

        // Show selected link
        if (_linkUrl != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Row(
              children: [
                const Icon(Icons.link, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _linkUrl!,
                    style: const TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 16),
                  onPressed: _removeLink,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMediaButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    Color? gradientStart,
    Color? gradientEnd,
  }) {
    final startColor = gradientStart ?? const Color(0xFF667eea);
    final endColor = gradientEnd ?? const Color(0xFF764ba2);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: startColor, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ShaderMask(
              shaderCallback:
                  (bounds) => LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [startColor, endColor],
                  ).createShader(bounds),
              child: Icon(icon, size: 18, color: Colors.white),
            ),
            const SizedBox(width: 6),
            ShaderMask(
              shaderCallback:
                  (bounds) => LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [startColor, endColor],
                  ).createShader(bounds),
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showEmojiPicker() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            height: 250,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Select Emoji',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 8,
                    children:
                        [
                              '😀',
                              '😃',
                              '😄',
                              '😁',
                              '😆',
                              '😅',
                              '😂',
                              '🤣',
                              '😊',
                              '😇',
                              '🙂',
                              '🙃',
                              '😉',
                              '😌',
                              '😍',
                              '🥰',
                              '😘',
                              '😗',
                              '😙',
                              '😚',
                              '😋',
                              '😛',
                              '😝',
                              '😜',
                              '🤪',
                              '🤨',
                              '🧐',
                              '🤓',
                              '😎',
                              '🤩',
                              '🥳',
                              '😏',
                              '😒',
                              '😞',
                              '😔',
                              '😟',
                              '😕',
                              '🙁',
                              '☹️',
                              '😣',
                              '😖',
                              '😫',
                              '😩',
                              '🥺',
                              '😢',
                              '😭',
                              '😤',
                              '😠',
                              '😡',
                              '🤬',
                              '🤯',
                              '😳',
                              '🥵',
                              '🥶',
                              '😱',
                              '😨',
                              '😰',
                              '😥',
                              '😓',
                              '🤗',
                              '🤔',
                              '🤭',
                              '🤫',
                              '🤥',
                            ]
                            .map(
                              (emoji) => GestureDetector(
                                onTap: () {
                                  _insertEmoji(emoji);
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  margin: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: Colors.grey[100],
                                  ),
                                  child: Center(
                                    child: Text(
                                      emoji,
                                      style: const TextStyle(fontSize: 20),
                                    ),
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                  ),
                ),
              ],
            ),
          ),
    );
  }
}
