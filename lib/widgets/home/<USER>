import 'package:flutter/material.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/widgets/top_paid_post_container.dart';
import 'package:money_mouthy_two/screens/post_detail_screen.dart';
import 'category_data.dart';
import 'horizontal_categories_section.dart';
import 'posts_feed.dart';

/// Explore Tab Widget
class ExploreTab extends StatelessWidget {
  final int currentCategoryIndex;
  final List<CategoryData> categories;
  final Function(int) onCategorySelected;

  const ExploreTab({
    super.key,
    required this.currentCategoryIndex,
    required this.categories,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final PostService postService = PostService();
    final currentCategory = categories[currentCategoryIndex].name;
    final topPost = postService.getTopPaidPostForCategory(currentCategory);

    print(
      'ExploreTab: Building for category "$currentCategory", topPost: ${topPost != null ? "found" : "null"}',
    );

    return Column(
      children: [
        HorizontalCategoriesSection(
          currentCategoryIndex: currentCategoryIndex,
          categories: categories,
          onCategorySelected: onCategorySelected,
        ),
        // Top Paid Post Container (24-hour system)
        if (topPost != null)
          TopPaidPostContainer(
            category: currentCategory,
            topPost: topPost,
            onTap: () {
              // Navigate to post detail view
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PostDetailScreen(post: topPost),
                ),
              );
            },
          ),
        Expanded(child: PostsFeed(category: currentCategory)),
      ],
    );
  }
}
